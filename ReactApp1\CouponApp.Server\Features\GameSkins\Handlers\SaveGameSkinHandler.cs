using CouponApp.Server.Data;
using CouponApp.Server.Models.Errors;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;
using System.Text.Json;

namespace CouponApp.Server.Features.GameSkins.Handlers;

public class SaveGameSkinHandler : IRequestHandler<SaveGameSkinCommand, OneOf<GameSkinDto, Error<string>>>
{
    private readonly ApplicationDbContext _context;
    private readonly IAdminService _adminService;

    public SaveGameSkinHandler(ApplicationDbContext context, IAdminService adminService)
    {
        _context = context;
        _adminService = adminService;
    }

    public async ValueTask<OneOf<GameSkinDto, Error<string>>> Handle(SaveGameSkinCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(request.CreateDto.Name))
            {
                return new Error<string>("Name is required");
            }

            if (string.IsNullOrWhiteSpace(request.CreateDto.GameId))
            {
                return new Error<string>("GameId is required");
            }

            if (request.CreateDto.ConfigOverrides == null)
            {
                return new Error<string>("ConfigOverrides is required");
            }

            // Check admin authorization for PublicDefault save mode
            if (request.CreateDto.SaveMode == SkinSaveMode.PublicDefault)
            {
                var isAdmin = await _adminService.IsAdminAsync(request.UserId);
                if (!isAdmin)
                {
                    return new Error<string>("Only administrators can create public default skins");
                }
            }

            // Determine OrganizationId based on save mode
            Guid? organizationId = request.CreateDto.SaveMode switch
            {
                SkinSaveMode.PublicDefault => null, // Public skins have null OrganizationId
                SkinSaveMode.Organization => request.CreateDto.OrganizationId,
                _ => request.CreateDto.OrganizationId
            };

            // Create new GameSkin entity
            var gameSkin = new GameSkin
            {
                Id = Guid.NewGuid(),
                Name = request.CreateDto.Name.Trim(),
                GameId = request.CreateDto.GameId.Trim(),
                ConfigOverrides = request.CreateDto.ConfigOverrides,
                OrganizationId = organizationId
            };

            _context.GameSkins.Add(gameSkin);
            await _context.SaveChangesAsync(cancellationToken);

            // Return DTO
            var dto = new GameSkinDto
            {
                Id = gameSkin.Id,
                Name = gameSkin.Name,
                GameId = gameSkin.GameId,
                ConfigOverrides = gameSkin.ConfigOverrides,
                OrganizationId = gameSkin.OrganizationId
            };

            return dto;
        }
        catch (Exception ex)
        {
            return new Error<string>($"Failed to save game skin: {ex.Message}");
        }
    }
}
