using CouponApp.Server.Data;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Services;

public interface IAdminService
{
    Task<bool> IsAdminAsync(string userId);
}

public class AdminService : IAdminService
{
    private readonly ApplicationDbContext _context;
    private const string AdminEmail = "<EMAIL>";

    public AdminService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<bool> IsAdminAsync(string userId)
    {
        var user = await _context.UserProfiles
            .FirstOrDefaultAsync(u => u.Id == userId);
        
        return user?.Email?.Equals(AdminEmail, StringComparison.OrdinalIgnoreCase) == true;
    }
    
}
