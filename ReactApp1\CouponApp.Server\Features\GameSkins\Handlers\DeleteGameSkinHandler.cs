using CouponApp.Server.Data;
using CouponApp.Server.Models.Errors;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.GameSkins.Handlers;

public class DeleteGameSkinHandler : IRequestHandler<DeleteGameSkinCommand, OneOf<Success, NotFound, UnauthorizedError>>
{
    private readonly ApplicationDbContext _context;
    private readonly IAdminService _adminService;

    public DeleteGameSkinHandler(ApplicationDbContext context, IAdminService adminService)
    {
        _context = context;
        _adminService = adminService;
    }

    public async ValueTask<OneOf<Success, NotFound, UnauthorizedError>> Handle(DeleteGameSkinCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var gameSkin = await _context.GameSkins
                .FirstOrDefaultAsync(gs => gs.Id == request.Id, cancellationToken);

            if (gameSkin == null)
            {
                return new NotFound();
            }

            // Authorization logic:
            // - Public skins (OrganizationId == null) can only be deleted by admins
            // - Organization skins can be deleted by organization members (for now, allowing all users)
            if (gameSkin.OrganizationId == null)
            {
                // This is a public skin, only admins can delete it
                var isAdmin = await _adminService.IsAdminAsync(request.UserId);
                if (!isAdmin)
                {
                    return new UnauthorizedError("Only administrators can delete public skins");
                }
            }
            // For organization skins, we allow deletion for now
            // In the future, you might want to check organization membership

            _context.GameSkins.Remove(gameSkin);
            await _context.SaveChangesAsync(cancellationToken);

            return new Success();
        }
        catch (Exception)
        {
            return new UnauthorizedError("Failed to delete game skin");
        }
    }
}
