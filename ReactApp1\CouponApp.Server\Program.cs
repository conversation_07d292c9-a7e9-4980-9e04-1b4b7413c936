using System.Net.Http.Headers;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using CouponApp.Server.Data;
using CouponApp.Server.Handlers;
using CouponApp.Server.Services;
using CouponApp.Server.Services.Executors;
using CouponApp.Server.Services.Integrations;
using CouponApp.Server.Services.NodeExecution;
using CouponApp.Server.Services.NodeExecution.Interfaces;
using Medallion.Threading;
using Medallion.Threading.Postgres;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Options;
using Minio;
using Npgsql;
using Resend;

namespace CouponApp.Server
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);
            var connectionString = builder.Configuration.GetConnectionString("ApplicationDbContextConnection") ?? throw new InvalidOperationException("Connection string 'ApplicationDbContextConnection' not found.");

            builder.Services.Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
                // Trust all proxies (adjust this if you have a specific proxy server)
                options.KnownNetworks.Clear();
                options.KnownProxies.Clear();
            });
            
            builder.Services.AddDbContext<ApplicationDbContext>(options => options.UseNpgsql(connectionString,
                npSqlOptions =>
                {
                }));
            
            var defaultCORSPolicyName = "Wasm";
            builder.Services.AddCors(options =>
            {
                var policy = new CorsPolicyBuilder();
                policy.AllowAnyOrigin()
                    .AllowAnyHeader()
                    .AllowAnyMethod();

                options.AddPolicy(name: defaultCORSPolicyName, policy.Build());
            });

            
            builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddJwtBearer(o =>
            {
                o.Authority = "https://auth.beakbyte.com/oidc";
                o.Audience = "https://dev.couponapp.com";
                o.RequireHttpsMetadata = false;
                
            });
            NpgsqlConnection.GlobalTypeMapper.EnableDynamicJson();

            builder.Services.AddControllers().AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.WriteIndented = true;
            });
            
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen();

            builder.Services.AddMinio(o =>
            {
                 o.WithSSL()
                     .WithEndpoint(builder.Configuration["Minio:Endpoint"])
                    .WithCredentials(builder.Configuration["Minio:AccessKey"], builder.Configuration["Minio:SecretKey"])
                    .Build();
            });
            
            builder.Services.AddHttpClient();
            builder.Services.AddDistributedMemoryCache();
            builder.Services.AddMemoryCache();
            
            // Add PostgresDistributedLock provider using the same connection string as the application
            builder.Services.AddSingleton<IDistributedLockProvider>(sp => 
                new PostgresDistributedSynchronizationProvider(connectionString));
            
            builder.Services.AddMediator(options =>
            {
                options.NotificationPublisherType = typeof(FireAndForgetNotificationPublisher);
                options.ServiceLifetime = ServiceLifetime.Transient;
            });
            
            builder.Services.AddSingleton<INangoClient, NangoClient>();
            builder.Services.AddScoped<PermissionService>();
            builder.Services.AddScoped<INodeExecutionService, NodeExecutionService>();
            builder.Services.AddScoped<INodeExecutorFactory, NodeExecutorFactory>();
            builder.Services.AddScoped<WebhookNodeExecutor>();
            builder.Services.AddScoped<SendFormToHubspotNodeExecutor>();
            builder.Services.AddScoped<MailchimpAddToListNodeExecutor>();
            builder.Services.AddScoped<ShopifyAddCustomerNodeExecutor>();
            
            builder.Services.AddScoped<IVariablesStringInterpolationService, VariablesStringInterpolationService>();
            builder.Services.AddScoped<IEmailService, EmailService>();
            builder.Services.AddScoped<ICloudflareService, CloudflareService>();
            builder.Services.AddScoped<IUserProfileService, UserProfileService>();
            builder.Services.AddScoped<ICampaignSessionService, CampaignSessionService>();
            builder.Services.AddHostedService<CustomDomainVerificationService>();
            builder.Services.AddSingleton<INangoProxyServiceFactory, NangoProxyServiceFactory>();
            builder.Services.AddScoped<INangoConnectionIdService, NangoConnectionIdService>();
            builder.Services.AddScoped<IUserManagementService, UserManagementService>();
            builder.Services.AddScoped<IAdminService, AdminService>();


            builder.Services.AddTransient<IPlausibleService, PlausibleService>();
           
            builder.Services.AddHttpClient("PlausibleApi", (serviceProvider, client) =>
            {
                var configuration = serviceProvider.GetRequiredService<IConfiguration>();
                var plausibleApiKey = configuration["Plausible:ApiKey"];
                // client.BaseAddress = new Uri("https://plausible.io/");
                client.BaseAddress = new Uri("https://plausible-dev.beakbyte.com");
                client.DefaultRequestHeaders.Add("Authorization", $"Bearer {plausibleApiKey}");
            });
            
            
            builder.Services.Configure<CloudflareConfiguration>(builder.Configuration.GetSection("Cloudflare"));
        
            
            // Add HttpClient for CloudflareService with Polly Retry Policy
            builder.Services.AddHttpClient<ICloudflareService, CloudflareService>((serviceProvider, client) =>
                {
                    var config = serviceProvider.GetRequiredService<IOptions<CloudflareConfiguration>>().Value;
                    client.BaseAddress = new Uri("https://api.cloudflare.com/client/v4/");
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {config.ApiKey}");
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                })
                .ConfigureHttpClient(client =>
                {
                    client.Timeout = TimeSpan.FromSeconds(30);
                })
                .SetHandlerLifetime(TimeSpan.FromMinutes(5));
            
            
            builder.Services.AddTransient<LogtoAuthHandler>();
            
            builder.Services.AddHttpClient("LogtoClient", client =>
                {
                    client.BaseAddress = new Uri(builder.Configuration["Logto:BaseUrl"]);
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                })
                .AddHttpMessageHandler<LogtoAuthHandler>();
            
            
            builder.Services.AddOptions();
            builder.Services.AddHttpClient<ResendClient>();
            builder.Services.Configure<ResendClientOptions>( o =>
            {
                o.ApiToken = "re_X37XMGky_4WQu8qt86kXuN6NsnpAgZttT";
            } );
            
            builder.Services.AddTransient<IResend, ResendClient>();
                
            var app = builder.Build();
            
                
            app.UseForwardedHeaders();
            app.UseDefaultFiles();
            app.UseStaticFiles();
      
            app.MapPost("api/auth/logout", async (SignInManager<UserProfile> signInManager) =>
            {
                await signInManager.SignOutAsync();
                return Results.Ok();

            }).RequireAuthorization();

            
     
            app.UseHttpsRedirection();
            app.UseAuthentication();
            app.UseAuthorization();
            app.MapControllers(); 
            app.MapFallbackToFile("/index.html");
            
            app.Run();
        }
    }
}
