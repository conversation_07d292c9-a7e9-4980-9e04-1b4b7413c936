using System.Text.Json;

namespace CouponApp.Server.Features.GameSkins;

public class GameSkinDto
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string GameId { get; set; }
    public object ConfigOverrides { get; set; }
    public Guid? OrganizationId { get; set; }
}

public class CreateGameSkinDto
{
    public string Name { get; set; }
    public string GameId { get; set; }
    public object ConfigOverrides { get; set; }
    public Guid? OrganizationId { get; set; }
    public SkinSaveMode SaveMode { get; set; } = SkinSaveMode.Organization;
}

public class GameSkinsResponseDto
{
    public List<GameSkinDto> GameSkins { get; set; } = new();
}
