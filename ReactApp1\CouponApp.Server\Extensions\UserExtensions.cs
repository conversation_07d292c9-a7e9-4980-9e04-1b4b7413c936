﻿using System.Security.Claims;

namespace CouponApp.Server.Extensions;

public static class UserExtensions
{
    public static string GetId(this ClaimsPrincipal user)
    {
        var id = user.FindFirstValue(ClaimTypes.NameIdentifier);
        return id ?? throw new InvalidOperationException("Error getting user data.");
    }

    public static string? GetEmail(this ClaimsPrincipal user)
    {
        return user.FindFirstValue(ClaimTypes.Email);
    }
}