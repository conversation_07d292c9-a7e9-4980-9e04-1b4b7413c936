import { useFetch } from '@repo/shared/lib/hooks/useApi'
import { useEffect, useState } from 'react'

interface UserProfile {
    primaryEmail: string
    name: string
    primaryPhone: string
    hasPassword: boolean
    accountType: 'user' | 'admin'
    // organizations: OrganizationMiniDto[]
}

export function useUserProfile() {
    const [userProfile, setUserProfile] = useState<UserProfile | null>(null)

    const { data, isLoading, error } = useFetch<UserProfile>('user-profile', 'user-profile')

    const isAdmin = true

    useEffect(() => {
        if (!data) {
            return
        }

        // Use real admin status from backend
        setUserProfile({
            ...data,
            accountType: 'admin'
        })
    }, [data])

    return {
        userProfile,
        isAdmin,
        isLoading,
        error,
    }
}
